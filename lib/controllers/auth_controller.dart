import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:money_mouthy_two/controllers/wallet_controller.dart';

/// Unified GetX Controller for managing authentication and profile state across the entire app
class AuthController extends GetxController {
  static AuthController get instance => Get.find();

  // Firebase instances
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Reactive variables for authentication state
  final _isAuthenticated = false.obs;
  final _isEmailVerified = false.obs;
  final _isProfileComplete = false.obs;
  final _isLoading = false.obs;
  final _errorMessage = ''.obs;
  final _currentUser = Rxn<User>();
  final _isInitialized = false.obs;

  // Profile completion flags
  final _hasUsername = false.obs;
  final _hasDisplayName = false.obs;
  final _profileCompletedFlag = false.obs;

  // Profile data reactive variables
  final _currentUserId = ''.obs;
  final _profileImageUrl = ''.obs;
  final _displayName = ''.obs;
  final _username = ''.obs;
  final _bio = ''.obs;
  final _email = ''.obs;
  final _postsCount = 0.obs;
  final _followersCount = 0.obs;
  final _followingCount = 0.obs;
  final _walletBalance = 0.0.obs;

  // Cache for other users' profile data
  final _userProfileCache = <String, Map<String, dynamic>>{}.obs;

  // Completer for initialization
  Completer<void>? _initializationCompleter;

  // Getters for authentication state
  bool get isAuthenticated => _isAuthenticated.value;
  bool get isEmailVerified => _isEmailVerified.value;
  bool get isProfileComplete => _isProfileComplete.value;
  bool get isLoading => _isLoading.value;
  String get errorMessage => _errorMessage.value;
  User? get currentUser => _currentUser.value;
  bool get hasUsername => _hasUsername.value;
  bool get hasDisplayName => _hasDisplayName.value;
  bool get profileCompletedFlag => _profileCompletedFlag.value;
  bool get isInitialized => _isInitialized.value;

  // Getters for profile data
  String get currentUserId => _currentUserId.value;
  String get profileImageUrl => _profileImageUrl.value;
  String get displayName => _displayName.value;
  String get username => _username.value;
  String get bio => _bio.value;
  String get email => _email.value;
  int get postsCount => _postsCount.value;
  int get followersCount => _followersCount.value;
  int get followingCount => _followingCount.value;
  double get walletBalance => _walletBalance.value;

  // Reactive getters for UI binding
  RxString get currentUserIdRx => _currentUserId;
  RxString get profileImageUrlRx => _profileImageUrl;
  RxString get displayNameRx => _displayName;
  RxString get usernameRx => _username;
  RxString get bioRx => _bio;
  RxString get emailRx => _email;
  RxBool get isLoadingRx => _isLoading;
  RxInt get postsCountRx => _postsCount;
  RxInt get followersCountRx => _followersCount;
  RxInt get followingCountRx => _followingCount;
  RxDouble get walletBalanceRx => _walletBalance;

  // Controllers
  WalletController get _walletController => Get.find<WalletController>();

  @override
  void onInit() {
    super.onInit();
    _initializeAuthListener();
    debugPrint('AuthController: Initialized');
  }

  /// Wait for authentication state to be fully initialized
  Future<void> waitForInitialization() async {
    if (_isInitialized.value) return;

    if (_initializationCompleter != null) {
      return _initializationCompleter!.future;
    }

    _initializationCompleter = Completer<void>();

    // Wait for auth state to be determined
    final user = _auth.currentUser;
    if (user != null) {
      // User is already logged in, wait for profile data to load
      await _waitForProfileDataLoad();
    } else {
      // No user logged in, mark as initialized
      _markAsInitialized();
    }

    return _initializationCompleter!.future;
  }

  /// Wait for profile data to be loaded for authenticated user
  Future<void> _waitForProfileDataLoad() async {
    int attempts = 0;
    const maxAttempts = 20; // 20 seconds max wait

    while (attempts < maxAttempts) {
      // Check if profile data has been loaded or user is not authenticated
      if (!_isAuthenticated.value ||
          _hasUsername.value ||
          _hasDisplayName.value ||
          _profileCompletedFlag.value) {
        _markAsInitialized();
        return;
      }

      await Future.delayed(const Duration(seconds: 1));
      attempts++;
    }

    // Timeout reached, mark as initialized anyway
    debugPrint(
      'AuthController: Timeout waiting for profile data, marking as initialized',
    );
    _markAsInitialized();
  }

  /// Mark the controller as initialized
  void _markAsInitialized() {
    if (!_isInitialized.value) {
      _isInitialized.value = true;
      _initializationCompleter?.complete();
      debugPrint('AuthController: Marked as initialized');
    }
  }

  @override
  void onClose() {
    super.onClose();
    debugPrint('AuthController: Disposed');
  }

  /// Initialize Firebase auth state listener
  void _initializeAuthListener() {
    _auth.authStateChanges().listen((User? user) async {
      debugPrint('AuthController: Auth state changed - User: ${user?.uid}');

      // Always clear previous user data first to prevent showing wrong data
      _clearCurrentUserData();

      _currentUser.value = user;

      if (user != null) {
        _isAuthenticated.value = true;
        _isEmailVerified.value = user.emailVerified;

        debugPrint(
          'AuthController: User authenticated - UID: ${user.uid}, EmailVerified: ${user.emailVerified}',
        );

        if (user.emailVerified) {
          debugPrint(
            'AuthController: Loading profile status for verified user...',
          );
          await _loadUserProfileStatus();
          await _initializeUserData();

          debugPrint(
            'AuthController: Profile loading complete - HasUsername: ${_hasUsername.value}, HasDisplayName: ${_hasDisplayName.value}, ProfileComplete: ${_isProfileComplete.value}',
          );
        } else {
          debugPrint(
            'AuthController: Email not verified, resetting profile flags',
          );
          _resetProfileFlags();
        }
      } else {
        debugPrint('AuthController: No user, resetting auth state');
        _resetAuthState();
      }

      // Mark as initialized after auth state is processed
      _markAsInitialized();
    });
  }

  /// Reset authentication state
  void _resetAuthState() {
    _isAuthenticated.value = false;
    _isEmailVerified.value = false;
    _resetProfileFlags();
    _errorMessage.value = '';

    // Reset wallet controller when user logs out
    try {
      _walletController.reset();
    } catch (e) {
      debugPrint('AuthController: Error resetting wallet controller: $e');
    }

    debugPrint('AuthController: Auth state reset');
  }

  /// Reset profile completion flags
  void _resetProfileFlags() {
    _isProfileComplete.value = false;
    _hasUsername.value = false;
    _hasDisplayName.value = false;
    _profileCompletedFlag.value = false;
  }

  /// Load user profile completion status from Firestore with retry mechanism
  Future<void> _loadUserProfileStatus() async {
    const maxRetries = 3;
    int retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        final user = _currentUser.value;
        if (user == null) return;

        final userDoc = await _firestore
            .collection('users')
            .doc(user.uid)
            .get()
            .timeout(const Duration(seconds: 20));

        if (userDoc.exists) {
          final userData = userDoc.data()!;

          _profileCompletedFlag.value = userData['profileCompleted'] ?? false;

          // More robust username check
          final username = userData['username']?.toString() ?? '';
          _hasUsername.value = username.trim().isNotEmpty;

          // More robust display name check
          final displayName = userData['name']?.toString() ?? '';
          _hasDisplayName.value = displayName.trim().isNotEmpty;

          // Enhanced profile completion check
          _isProfileComplete.value =
              _profileCompletedFlag.value ||
              (_hasUsername.value &&
                  _hasDisplayName.value &&
                  _isEmailVerified.value);

          debugPrint(
            'AuthController: Profile status loaded - Complete: ${_isProfileComplete.value}, HasUsername: ${_hasUsername.value} (username: "$username"), HasDisplayName: ${_hasDisplayName.value} (name: "$displayName"), EmailVerified: ${_isEmailVerified.value}, ProfileCompletedFlag: ${_profileCompletedFlag.value}',
          );
          return; // Success, exit retry loop
        } else {
          _resetProfileFlags();
          debugPrint('AuthController: User document not found');
          return; // No point retrying if document doesn't exist
        }
      } catch (e) {
        retryCount++;
        debugPrint(
          'AuthController: Error loading profile status (attempt $retryCount): $e',
        );

        if (retryCount >= maxRetries) {
          debugPrint(
            'AuthController: Failed to load profile status after $maxRetries attempts',
          );
          _resetProfileFlags();
          return;
        }

        // Wait before retrying with exponential backoff
        await Future.delayed(Duration(seconds: retryCount * 2));
      }
    }
  }

  /// Initialize user data and WalletController
  Future<void> _initializeUserData() async {
    try {
      debugPrint('AuthController: Initializing user data...');

      // Initialize current user profile data
      await _initializeCurrentUserProfile();

      // Initialize WalletController if profile is complete
      if (_isProfileComplete.value) {
        if (!_walletController.isInitialized) {
          await _walletController.initialize();
        }
      }

      debugPrint('AuthController: User data initialization complete');
    } catch (e) {
      debugPrint('AuthController: Error initializing user data: $e');
    }
  }

  /// Initialize current user profile data
  Future<void> _initializeCurrentUserProfile() async {
    try {
      // First, clear any previous user data to prevent showing old data
      _clearCurrentUserDataOnly();

      final user = _currentUser.value;
      if (user != null) {
        _currentUserId.value = user.uid;
        _email.value = user.email ?? '';

        debugPrint('AuthController: Loading profile data for user ${user.uid}');
        await _loadCurrentUserProfile();
        await _loadCurrentUserStats();
      }
    } catch (e) {
      debugPrint('AuthController: Error initializing current user profile: $e');
    }
  }

  /// Sign in with email and password
  Future<bool> signInWithEmailAndPassword(String email, String password) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      debugPrint('AuthController: Attempting login for $email');

      final credential = await _auth
          .signInWithEmailAndPassword(email: email, password: password)
          .timeout(const Duration(seconds: 30));

      // Reload user to get latest emailVerified flag
      await credential.user?.reload().timeout(const Duration(seconds: 20));
      final user = _auth.currentUser;

      if (user == null) {
        throw FirebaseAuthException(
          code: 'user-not-found',
          message: 'User account not found',
        );
      }

      if (!user.emailVerified) {
        await user.sendEmailVerification().timeout(const Duration(seconds: 20));
        await _auth.signOut();
        _errorMessage.value =
            'Please verify your email first. A new verification link has been sent.';
        return false;
      }

      // Update last login timestamp
      try {
        await _firestore
            .collection('users')
            .doc(user.uid)
            .set({
              'emailVerified': true,
              'lastLogin': FieldValue.serverTimestamp(),
            }, SetOptions(merge: true))
            .timeout(const Duration(seconds: 20));
      } catch (e) {
        debugPrint('AuthController: Unable to update last login: $e');
      }

      // Wait for auth state listener to process and load profile data
      debugPrint(
        'AuthController: Login successful, waiting for profile data...',
      );
      await _waitForAuthStateProcessing();

      debugPrint('AuthController: Login process completed for ${user.uid}');
      return true;
    } on FirebaseAuthException catch (e) {
      debugPrint(
        'AuthController: Firebase auth error: ${e.code} - ${e.message}',
      );
      _errorMessage.value = _getAuthErrorMessage(e.code);
      return false;
    } catch (e) {
      debugPrint('AuthController: Login error: $e');
      _errorMessage.value = 'Login failed. Please try again.';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Wait for auth state listener to process the login and load profile data
  Future<void> _waitForAuthStateProcessing() async {
    int attempts = 0;
    const maxAttempts = 15; // 15 seconds max wait

    while (attempts < maxAttempts) {
      // Check if auth state has been processed
      if (_isAuthenticated.value && _isEmailVerified.value) {
        // Give a bit more time for profile data to load
        await Future.delayed(const Duration(milliseconds: 500));
        return;
      }

      await Future.delayed(const Duration(seconds: 1));
      attempts++;
    }

    debugPrint('AuthController: Timeout waiting for auth state processing');
  }

  /// Clear error message
  void clearError() {
    _errorMessage.value = '';
  }

  /// Get user-friendly error message from Firebase auth error code
  String _getAuthErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'user-not-found':
        return 'No account found with this email address.';
      case 'wrong-password':
        return 'Incorrect password. Please try again.';
      case 'invalid-email':
        return 'Please enter a valid email address.';
      case 'user-disabled':
        return 'This account has been disabled.';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'network-request-failed':
        return 'Network error. Please check your connection.';
      default:
        return 'Authentication failed. Please try again.';
    }
  }

  /// Sign out user and clear all data
  Future<void> signOut() async {
    try {
      _isLoading.value = true;
      debugPrint('AuthController: Signing out user');

      // Clear all controller data
      _clearCurrentUserData();

      // Reset wallet controller
      try {
        _walletController.reset();
      } catch (e) {
        debugPrint(
          'AuthController: Error resetting wallet during sign out: $e',
        );
      }

      // Sign out from Firebase
      await _auth.signOut();

      debugPrint('AuthController: Sign out complete');
    } catch (e) {
      debugPrint('AuthController: Error during sign out: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Create account with email and password
  Future<bool> createAccountWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      _isLoading.value = true;
      _errorMessage.value = '';

      debugPrint('AuthController: Creating account for $email');

      final credential = await _auth
          .createUserWithEmailAndPassword(email: email, password: password)
          .timeout(const Duration(seconds: 30));

      final user = credential.user;
      if (user != null) {
        // Send email verification
        await user.sendEmailVerification().timeout(const Duration(seconds: 20));
        debugPrint('AuthController: Account created, verification email sent');
        return true;
      }

      return false;
    } on FirebaseAuthException catch (e) {
      debugPrint(
        'AuthController: Firebase auth error: ${e.code} - ${e.message}',
      );
      _errorMessage.value = _getAuthErrorMessage(e.code);
      return false;
    } catch (e) {
      debugPrint('AuthController: Account creation error: $e');
      _errorMessage.value = 'Account creation failed. Please try again.';
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update profile completion status
  Future<void> updateProfileCompletion({
    bool? hasUsername,
    bool? hasDisplayName,
    bool? profileCompleted,
  }) async {
    if (hasUsername != null) _hasUsername.value = hasUsername;
    if (hasDisplayName != null) _hasDisplayName.value = hasDisplayName;
    if (profileCompleted != null) {
      _profileCompletedFlag.value = profileCompleted;
    }

    // Update overall profile completion status
    _isProfileComplete.value =
        _profileCompletedFlag.value ||
        (_hasUsername.value && _hasDisplayName.value && _isEmailVerified.value);

    debugPrint(
      'AuthController: Profile completion updated - Complete: ${_isProfileComplete.value}',
    );

    // Initialize wallet if profile is now complete
    if (_isProfileComplete.value && !_walletController.isInitialized) {
      try {
        await _walletController.initialize();
      } catch (e) {
        debugPrint(
          'AuthController: Error initializing wallet after profile completion: $e',
        );
      }
    }
  }

  /// Refresh user profile status from Firestore
  Future<void> refreshProfileStatus() async {
    debugPrint('AuthController: Refreshing profile status...');
    await _loadUserProfileStatus();
    await _initializeUserData();
  }

  /// Force reload profile data (useful for debugging)
  Future<void> forceReloadProfile() async {
    if (_currentUser.value != null) {
      debugPrint('AuthController: Force reloading profile data...');
      _resetProfileFlags();
      await _loadUserProfileStatus();
      await _initializeUserData();
    }
  }

  /// Wait for profile status to be loaded and return navigation route
  Future<String> getNavigationRouteAsync() async {
    // Wait for profile status to be loaded if user is authenticated
    if (_isAuthenticated.value && _isEmailVerified.value) {
      // Give some time for the auth state listener to process
      int attempts = 0;
      while (attempts < 10) {
        await Future.delayed(const Duration(milliseconds: 1000));

        // Check if profile status has been loaded
        if (_hasUsername.value ||
            _hasDisplayName.value ||
            _profileCompletedFlag.value) {
          break;
        }

        attempts++;
      }
    }

    return getNavigationRoute();
  }

  /// Get navigation route based on current auth and profile state
  String getNavigationRoute() {
    debugPrint(
      'AuthController: getNavigationRoute - Authenticated: ${_isAuthenticated.value}, EmailVerified: ${_isEmailVerified.value}, ProfileComplete: ${_isProfileComplete.value}, HasUsername: ${_hasUsername.value}, HasDisplayName: ${_hasDisplayName.value}',
    );

    if (!_isAuthenticated.value || !_isEmailVerified.value) {
      return kIsWeb ? '/landing' : '/signup';
    }

    if (_isProfileComplete.value) {
      debugPrint('AuthController: Navigating to /home');
      return '/home';
    } else if (!_hasUsername.value) {
      debugPrint(
        'AuthController: Navigating to /choose_username (no username)',
      );
      return '/choose_username';
    } else {
      debugPrint(
        'AuthController: Navigating to /create_profile (has username, missing other data)',
      );
      return '/create_profile';
    }
  }

  /// Check if user needs to complete onboarding
  bool get needsOnboarding =>
      _isAuthenticated.value &&
      _isEmailVerified.value &&
      !_isProfileComplete.value;

  /// Check if user is ready for main app
  bool get isReadyForMainApp =>
      _isAuthenticated.value &&
      _isEmailVerified.value &&
      _isProfileComplete.value;

  // ===== PROFILE MANAGEMENT METHODS =====

  /// Load current user's profile data from Firestore
  Future<void> _loadCurrentUserProfile() async {
    try {
      if (_currentUserId.value.isEmpty) {
        debugPrint('AuthController: Cannot load profile - no current user ID');
        return;
      }

      debugPrint(
        'AuthController: Loading profile for user ${_currentUserId.value}',
      );

      final userDoc =
          await _firestore.collection('users').doc(_currentUserId.value).get();

      if (userDoc.exists) {
        final userData = userDoc.data();
        if (userData != null && userData.isNotEmpty) {
          _updateCurrentUserData(userData);

          // Cache the current user's data
          _userProfileCache[_currentUserId.value] = userData;

          debugPrint(
            'AuthController: Loaded profile for ${_currentUserId.value} - Name: ${userData['name']}, Username: ${userData['username']}',
          );
        } else {
          debugPrint(
            'AuthController: Profile document exists but is empty for user ${_currentUserId.value}',
          );
        }
      } else {
        debugPrint(
          'AuthController: No profile document found for user ${_currentUserId.value}',
        );
      }
    } catch (e) {
      debugPrint('AuthController: Error loading current user profile: $e');
    }
  }

  /// Update current user reactive variables
  void _updateCurrentUserData(Map<String, dynamic> userData) {
    _profileImageUrl.value =
        userData['profileImageUrl'] ?? userData['photoUrl'] ?? '';
    _displayName.value = userData['name'] ?? userData['displayName'] ?? '';
    _username.value = userData['username'] ?? '';
    _bio.value = userData['bio'] ?? '';

    debugPrint(
      'AuthController: Updated reactive data - Name: ${_displayName.value}, Username: ${_username.value}, Bio: ${_bio.value}',
    );
  }

  /// Load current user's stats (posts, followers, following, wallet)
  Future<void> _loadCurrentUserStats() async {
    try {
      if (_currentUserId.value.isEmpty) return;

      // Load posts count
      final postsQuery =
          await _firestore
              .collection('posts')
              .where('authorId', isEqualTo: _currentUserId.value)
              .get();
      _postsCount.value = postsQuery.docs.length;

      // Load followers count
      final followersQuery =
          await _firestore
              .collection('follows')
              .where('followingId', isEqualTo: _currentUserId.value)
              .get();
      _followersCount.value = followersQuery.docs.length;

      // Load following count
      final followingQuery =
          await _firestore
              .collection('follows')
              .where('followerId', isEqualTo: _currentUserId.value)
              .get();
      _followingCount.value = followingQuery.docs.length;

      debugPrint(
        'AuthController: Loaded user stats - Posts: ${_postsCount.value}, Followers: ${_followersCount.value}, Following: ${_followingCount.value}',
      );
    } catch (e) {
      debugPrint('AuthController: Error loading user stats: $e');
    }
  }

  /// Clear only current user reactive variables (not cache)
  void _clearCurrentUserDataOnly() {
    _currentUserId.value = '';
    _profileImageUrl.value = '';
    _displayName.value = '';
    _username.value = '';
    _bio.value = '';
    _email.value = '';
    _postsCount.value = 0;
    _followersCount.value = 0;
    _followingCount.value = 0;
    _walletBalance.value = 0.0;
    debugPrint('AuthController: Current user reactive data cleared');
  }

  /// Clear current user data (called during logout)
  void _clearCurrentUserData() {
    _clearCurrentUserDataOnly();
    _userProfileCache.clear();
    debugPrint('AuthController: Current user data and cache cleared');
  }

  /// Update current user's profile data
  Future<void> updateCurrentUserProfile({
    String? profileImageUrl,
    String? displayName,
    String? username,
    String? bio,
  }) async {
    try {
      if (_currentUserId.value.isEmpty) return;

      _isLoading.value = true;

      final updateData = <String, dynamic>{};

      if (profileImageUrl != null) {
        updateData['profileImageUrl'] = profileImageUrl;
        debugPrint(
          'AuthController: Updating profile image URL from "${_profileImageUrl.value}" to "$profileImageUrl"',
        );
        _profileImageUrl.value = profileImageUrl;
        debugPrint(
          'AuthController: Profile image URL updated to "${_profileImageUrl.value}"',
        );
      }

      if (displayName != null) {
        updateData['name'] = displayName;
        updateData['displayName'] = displayName;
        _displayName.value = displayName;
      }

      if (username != null) {
        updateData['username'] = username;
        _username.value = username;
      }

      if (bio != null) {
        updateData['bio'] = bio;
        _bio.value = bio;
      }

      if (updateData.isNotEmpty) {
        updateData['updatedAt'] = FieldValue.serverTimestamp();

        await _firestore
            .collection('users')
            .doc(_currentUserId.value)
            .update(updateData);

        // Update cache
        _userProfileCache[_currentUserId.value] = {
          ..._userProfileCache[_currentUserId.value] ?? {},
          ...updateData,
        };

        debugPrint('AuthController: Updated current user profile');
      }
    } catch (e) {
      debugPrint('AuthController: Error updating current user profile: $e');
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Get profile data for any user (with caching)
  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    if (userId.isEmpty) return null;

    // Check cache first
    if (_userProfileCache.containsKey(userId)) {
      return _userProfileCache[userId];
    }

    try {
      final userDoc = await _firestore.collection('users').doc(userId).get();

      if (userDoc.exists) {
        final userData = userDoc.data()!;
        _userProfileCache[userId] = userData;
        return userData;
      }
    } catch (e) {
      debugPrint('AuthController: Error getting user profile for $userId: $e');
    }

    return null;
  }

  /// Get profile image URL for any user
  Future<String?> getUserProfileImage(String userId) async {
    final userData = await getUserProfile(userId);
    return userData?['profileImageUrl'] ?? userData?['photoUrl'];
  }

  /// Get display name for any user
  Future<String> getUserDisplayName(String userId) async {
    final userData = await getUserProfile(userId);
    return userData?['name'] ??
        userData?['displayName'] ??
        userData?['username'] ??
        'Unknown User';
  }

  /// Get username for any user
  Future<String> getUserUsername(String userId) async {
    final userData = await getUserProfile(userId);
    return userData?['username'] ?? '';
  }

  /// Clear cache for a specific user (useful when user updates profile)
  void clearUserCache(String userId) {
    _userProfileCache.remove(userId);
  }

  /// Clear all cached user data
  void clearAllCache() {
    _userProfileCache.clear();
  }

  /// Refresh current user data
  Future<void> refreshCurrentUser() async {
    await _loadCurrentUserProfile();
    await _loadCurrentUserStats();
  }

  /// Update wallet balance
  void updateWalletBalance(double balance) {
    _walletBalance.value = balance;
  }

  /// Update stats counts
  void updateStats({
    int? postsCount,
    int? followersCount,
    int? followingCount,
  }) {
    if (postsCount != null) _postsCount.value = postsCount;
    if (followersCount != null) _followersCount.value = followersCount;
    if (followingCount != null) _followingCount.value = followingCount;
  }

  /// Check if current user data is available
  bool get hasCurrentUserData =>
      _currentUserId.value.isNotEmpty && _username.value.isNotEmpty;

  /// Get current user's initials for avatar fallback
  String get currentUserInitials {
    if (_displayName.value.isNotEmpty) {
      return _displayName.value
          .split(' ')
          .map((e) => e[0])
          .take(2)
          .join()
          .toUpperCase();
    } else if (_username.value.isNotEmpty) {
      return _username.value.substring(0, 1).toUpperCase();
    }
    return 'U';
  }
}
